#include "teacher.h"

Teacher::Teacher(int empId, string name, string pwd)
{
	this->m_EmpId = empId;
	this->m_Name = name;
	this->m_Pwd = pwd;
}
Teacher::~Teacher()
{
}
void Teacher::operMenu()
{
	system("cls");
	cout << "============================" << endl;
	cout << "1.show all order" << endl;
	cout << "2.valid order" << endl;
	cout << "0.exit" << endl;
	cout << "============================" << endl;
	cout << "please input：";
}
void Teacher::showAllOrder()
{
}