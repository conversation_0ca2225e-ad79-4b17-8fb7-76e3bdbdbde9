#pragma once

#include <string>
#include <unordered_map>
using std::string;
using std::unordered_map;

enum UserType
{
	USER_STUDENT = 1,   // Student
	USER_TEACHER,    // Teacher
	USER_MANAGER  // Manager
};

// 用于存储用户类型信息的结构体
struct UserTypeInfo {
	string label;
	string fileName;

	UserTypeInfo() = default;
	UserTypeInfo(const string& l, const string& f) : label(l), fileName(f) {}
};

// 原有的类保持兼容性
class UserTypeClass
{
public:
	UserType userType;
	string label;
	string fileName;
	UserTypeClass(UserType type, string str, string file)
		:userType(type), label(str), fileName(file) {};
};
