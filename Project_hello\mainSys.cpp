#include <iostream>
#include <unordered_map>
#include <map>
#include <fstream>
#include <vector>
#include <limits>
#include <string>
#include <memory>
#include "enum.h"
#include "globalFile.h"
#include "Identity.h"
#include "student.h"
#include "teacher.h"
#include "manager.h"
#include "tool.h"

using namespace std;

bool judgmentPwd(int id, const string &name, const string &pwd, const string &fileName, bool isManager = false)
{
	ifstream ifs(fileName, ios::in);
	if (!ifs.is_open())
	{
		cout << "open file failed" << endl;
		return false;
	}
	vector<FileUserInfo> users;
	string fName, fPwd;
	// 一次性读取到内存
	if (isManager)
	{
		while (ifs >> fName >> fPwd)
		{
			users.emplace_back(0, fName, fPwd);
		}
	}
	else
	{
		int fId;
		while (ifs >> fId >> fName >> fPwd)
		{
			users.emplace_back(fId, fName, fPwd);
		}
	}
	ifs.close();

	// 判断
	auto it = find_if(users.begin(), users.end(), [&](const FileUserInfo &user)
					  { return user.id == id && user.name == name && user.pwd == pwd; });
	if (it != users.end())
	{
		system("cls");
		cout << "login success" << endl;
		return true;
	}
	else
	{
		cout << "login failed" << endl;
		return false;
	}
}

void login(unordered_map<int, UserTypeInfo>::iterator it)
{
	unique_ptr<Identity> person = nullptr; // 使用智能指针
	int id;
	string name;
	string pwd;

	UserType userType = static_cast<UserType>(it->first);
	if (userType == USER_STUDENT || userType == USER_TEACHER)
	{
		// 使用安全的输入函数
		id = getValidIntInput("please input ID: ");
		name = getValidStringInput("please input name: ");
		pwd = getValidStringInput("please input password: ");

		if (judgmentPwd(id, name, pwd, it->second.fileName))
		{
			// 使用智能指针，自动管理内存
			if (userType == USER_STUDENT) {
				person = make_unique<Student>(id, name, pwd);
			} else {
				person = make_unique<Teacher>(id, name, pwd);
			}
		}
	}
	else if (userType == USER_MANAGER)
	{
		// 使用安全的输入函数
		name = getValidStringInput("please input name: ");
		pwd = getValidStringInput("please input password: ");

		if (judgmentPwd(0, name, pwd, it->second.fileName, true))
		{
			person = make_unique<Manager>(name, pwd);
		}
	}

	if (person != nullptr) {
		person->operMenu(); // 调用操作菜单函数
	}
}

// 方案1：使用 unordered_map 存储完整的用户信息 - O(1) 查找效率
unordered_map<int, UserTypeInfo> userTypeMap = {
	{USER_STUDENT, {"Student", STUDENT_FILE}},
	{USER_TEACHER, {"Teacher", TEACHER_FILE}},
	{USER_MANAGER, {"Manager", ADMIN_FILE}}};

int main()
{
	int select = 0;
	const int WIDTH = 28;
	while (true)
	{
		system("cls");
		cout << "============================" << endl;
		// 遍历显示所有用户类型选项
		// 旧写法
		/*for (const auto& pair : userTypeMap) {
			cout << pair.first << "." << pair.second.label << endl;
		}*/
		// 新写法 C++20 结构化绑定
		for (const auto &[key, value] : userTypeMap)
		{
			const string s = to_string(key) + "." + value.label;
			cout.width(WIDTH); // 只作用到下一个cout，这样写b不行：cout << a << b
			cout << s << endl;
		}
		cout.width(WIDTH);
		cout << "0.exit" << endl;
		cout << "============================" << endl;
		// cout << "please input：";
		// 使用安全的整数输入函数处理菜单选择
		// if (!(cin >> select)) {
		// 	//cout << "输入错误！请输入一个有效的数字。" << endl;
		// 	cin.clear(); // 清除错误标志
		// 	cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 清除错误输入
		// 	//system("pause");
		// 	continue; // 重新开始循环
		// }
		select = getValidIntInput("please input：");

		// O(1) 时间复杂度查找
		auto it = userTypeMap.find(select);
		if (it != userTypeMap.end())
		{
			cout << "you select " << it->second.label << endl;
			cout << "file path: " << it->second.fileName << endl;
			login(it);
			system("pause");
		}
		else if (select == 0)
		{
			exit(0);
		}
		else
		{
			cout << "input error, please input again!" << endl;
			system("pause");
		}
	}
	return 0;
}