#include <iostream>
#include <string>
#include <limits>
#include "tool.h"

using namespace std;

// 安全的整数输入函数
int getValidIntInput(const string& prompt) {
    int value;
    while (true) {
        cout << prompt;
        if (cin >> value) {
            // 清除输入缓冲区中的剩余字符
            cin.ignore(numeric_limits<streamsize>::max(), '\n');
            return value;
        }
        else {
            cout << "输入错误！请输入一个有效的整数。" << endl;
            cin.clear(); // 清除错误标志
            cin.ignore(numeric_limits<streamsize>::max(), '\n'); // 清除错误输入
        }
    }
}

// 安全的字符串输入函数
string getValidStringInput(const string& prompt, bool allowEmpty) {
    string value;
    while (true) {
        cout << prompt;
        // 如果之前有输入操作，清除可能残留的换行符
        if (cin.peek() == '\n') {
            cin.ignore();
        }
        getline(cin, value);

        // 检查是否为空（如果不允许为空）
        if (!allowEmpty && value.empty()) {
            cout << "输入不能为空，请重新输入。" << endl;
            continue;
        }

        // 检查是否只包含空格
        if (!allowEmpty && value.find_first_not_of(' ') == string::npos) {
            cout << "输入不能只包含空格，请重新输入。" << endl;
            continue;
        }

        return value;
    }
}